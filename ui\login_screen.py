"""
شاشة الدخول المتقدمة والكبيرة للبرنامج
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QFrame, QApplication,
                             QGraphicsDropShadowEffect, QCheckBox, QSpacerItem,
                             QSizePolicy, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QColor, QPen, QLinearGradient
from database import get_session, User
import hashlib


class LoginScreen(QWidget):
    """شاشة الدخول المتقدمة والكبيرة"""
    
    # إشارة لإرسال بيانات المستخدم عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object, object)  # session, user
    
    def __init__(self):
        super().__init__()
        self.session = None
        # متغيرات التحريك بالماوس
        self.dragging = False
        self.drag_position = None
        self.setup_ui()
        self.setup_animations()

        # التأكد من تفعيل النافذة والتفاعل
        self.setFocusPolicy(Qt.StrongFocus)
        self.activateWindow()
        self.raise_()
        
    def setup_ui(self):
        """إعداد واجهة شاشة الدخول المتقدمة"""
        # إعداد النافذة الأساسية - عرض أقل وارتفاع أكبر
        self.setWindowTitle("🔐 Smart Finish - نظام تسجيل الدخول المتطور")
        self.setFixedSize(600, 650)  # عرض أقل وارتفاع أكبر
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # التأكد من ظهور النافذة في المقدمة
        self.setWindowState(Qt.WindowActive)
        
        # إنشاء التخطيط الرئيسي مباشرة بدون إطار
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(25)

        # تطبيق الخلفية المتدرجة مباشرة على النافذة
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border-radius: 20px;
            }
        """)
        
        # إنشاء منطقة العنوان
        self.create_header_section(main_layout)

        # إنشاء منطقة تسجيل الدخول
        self.create_login_section(main_layout)

        # إنشاء منطقة الأزرار
        self.create_buttons_section(main_layout)

        # إنشاء منطقة التذييل
        self.create_footer_section(main_layout)
        
    def create_header_section(self, parent_layout):
        """إنشاء منطقة العنوان المتقدمة - ارتفاع أكبر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(150)  # ارتفاع أكبر
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)  # مسافة أكبر
        
        # العنوان الرئيسي - مكبر ومحسن
        title_label = QLabel("🏢 Smart Finish")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 42px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.5 rgba(255, 215, 0, 0.8),
                    stop:1 rgba(255, 255, 255, 0.9));
                -webkit-background-clip: text;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        
        # العنوان الفرعي - مقلل
        subtitle_label = QLabel("نظام المحاسبة الإداري")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 20px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 8px;
            }
        """)

        # إزالة الشعار لتوفير المساحة وإضافة العنوان والعنوان الفرعي فقط
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
        
    def create_login_section(self, parent_layout):
        """إنشاء منطقة تسجيل الدخول المتقدمة - ارتفاع أكبر"""
        login_frame = QFrame()
        login_frame.setFixedHeight(250)  # ارتفاع أكبر
        login_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(255, 255, 255, 0.1));
                border-radius: 15px;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        # إضافة تأثير الظل لإطار تسجيل الدخول
        login_shadow = QGraphicsDropShadowEffect()
        login_shadow.setBlurRadius(20)
        login_shadow.setColor(QColor(0, 0, 0, 50))
        login_shadow.setOffset(0, 5)
        login_frame.setGraphicsEffect(login_shadow)
        
        login_layout = QGridLayout(login_frame)
        login_layout.setContentsMargins(30, 25, 30, 25)  # مسافات أكبر
        login_layout.setSpacing(20)  # مسافة أكبر
        
        # تسمية اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet(self.get_label_style())
        
        # حقل اسم المستخدم - ارتفاع أكبر
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_input.setText("admin")  # قيمة افتراضية
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.setFixedHeight(50)  # ارتفاع أكبر
        self.username_input.setEnabled(True)  # التأكد من تفعيل الحقل
        self.username_input.setReadOnly(False)  # التأكد من إمكانية التحرير
        
        # تسمية كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setStyleSheet(self.get_label_style())
        
        # حقل كلمة المرور - ارتفاع أكبر
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        self.password_input.setText("admin")  # قيمة افتراضية
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.setFixedHeight(50)  # ارتفاع أكبر
        self.password_input.setEnabled(True)  # التأكد من تفعيل الحقل
        self.password_input.setReadOnly(False)  # التأكد من إمكانية التحرير
        
        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("🔄 تذكر بيانات الدخول")
        self.remember_checkbox.setChecked(True)
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                border: 2px solid #10B981;
            }
        """)
        
        # ترتيب العناصر في الشبكة
        login_layout.addWidget(username_label, 0, 0)
        login_layout.addWidget(self.username_input, 0, 1)
        login_layout.addWidget(password_label, 1, 0)
        login_layout.addWidget(self.password_input, 1, 1)
        login_layout.addWidget(self.remember_checkbox, 2, 0, 1, 2, Qt.AlignCenter)

        # ربط Enter بين الحقول
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.handle_login)

        parent_layout.addWidget(login_frame)
        
    def create_buttons_section(self, parent_layout):
        """إنشاء منطقة الأزرار المتقدمة - ارتفاع أكبر"""
        buttons_frame = QFrame()
        buttons_frame.setFixedHeight(120)  # ارتفاع أكبر
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)  # مسافات أكبر
        buttons_layout.setSpacing(30)  # مسافة أكبر
        
        # زر تسجيل الدخول - حجم أكبر
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setFixedSize(200, 60)  # حجم أكبر
        self.login_button.setEnabled(True)  # التأكد من تفعيل الزر
        self.style_advanced_button(self.login_button, 'emerald')
        self.login_button.clicked.connect(self.handle_login)

        # زر الإلغاء/الخروج - حجم أكبر
        self.cancel_button = QPushButton("❌ إغلاق")
        self.cancel_button.setFixedSize(250, 60)  # عرض 250px
        self.cancel_button.setEnabled(True)  # التأكد من تفعيل الزر
        self.style_advanced_button(self.cancel_button, 'danger')
        self.cancel_button.clicked.connect(self.close)
        
        # إضافة مساحة مرنة
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addStretch()
        
        parent_layout.addWidget(buttons_frame)

    def create_footer_section(self, parent_layout):
        """إنشاء منطقة التذييل المتقدمة - ارتفاع أكبر"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(80)  # ارتفاع مناسب للنص الترحيبي فقط
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(8)  # مسافة أكبر

        # النص الترحيبي المبسط والواضح
        welcome_label = QLabel("أهلاً بك في شركتنا - حيث يبدأ استثمارك بثقة ويكتمل عقارك بنجاح")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setWordWrap(True)  # السماح بتقسيم النص على أسطر متعددة
        welcome_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 8px;
                line-height: 1.4;
            }
        """)

        footer_layout.addWidget(welcome_label)

        parent_layout.addWidget(footer_frame)

    def get_label_style(self):
        """الحصول على نمط التسميات"""
        return """
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 5px;
            }
        """

    def get_input_style(self):
        """الحصول على نمط حقول الإدخال"""
        return """
            QLineEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 12px;
                padding: 12px 15px;
                font-size: 16px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
                color: #1E293B;
                selection-background-color: #3B82F6;
            }
            QLineEdit:focus {
                border: 2px solid #3B82F6;
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
            }
        """

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة - مطابق لقسم العملاء"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['emerald'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 14px;
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def setup_animations(self):
        """إعداد الرسوم المتحركة المتقدمة"""
        # رسوم متحركة لظهور النافذة
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # رسوم متحركة لحركة النافذة
        self.move_animation = QPropertyAnimation(self, b"geometry")
        self.move_animation.setDuration(800)
        self.move_animation.setEasingCurve(QEasingCurve.OutBack)

        # بدء الرسوم المتحركة عند الظهور
        QTimer.singleShot(100, self.start_entrance_animation)

        # تركيز حقل اسم المستخدم بعد انتهاء الرسوم المتحركة
        QTimer.singleShot(1200, self.set_initial_focus)

    def start_entrance_animation(self):
        """بدء رسوم متحركة للدخول"""
        # تحديد الموضع النهائي (وسط الشاشة)
        screen = QApplication.desktop().screenGeometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2

        # تحديد الموضع الابتدائي (أعلى الشاشة)
        start_rect = QRect(x, -self.height(), self.width(), self.height())
        end_rect = QRect(x, y, self.width(), self.height())

        # إعداد الرسوم المتحركة
        self.setGeometry(start_rect)
        self.move_animation.setStartValue(start_rect)
        self.move_animation.setEndValue(end_rect)

        # بدء الرسوم المتحركة
        self.fade_animation.start()
        self.move_animation.start()

    def set_initial_focus(self):
        """تعيين التركيز الأولي على حقل اسم المستخدم"""
        try:
            self.username_input.setFocus()
            self.username_input.selectAll()
        except Exception as e:
            print(f"خطأ في تعيين التركيز: {str(e)}")

    def handle_login(self):
        """معالجة عملية تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة البيانات
        if not username or not password:
            self.show_error_message("⚠️ خطأ في البيانات",
                                   "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                # نجح تسجيل الدخول
                self.show_success_message("✅ نجح تسجيل الدخول",
                                        f"مرحباً بك {user.full_name}")

                # إرسال إشارة نجاح تسجيل الدخول
                QTimer.singleShot(1500, lambda: self.login_successful.emit(self.session, user))
                QTimer.singleShot(1500, self.close)

            else:
                # فشل تسجيل الدخول
                self.show_error_message("❌ خطأ في تسجيل الدخول",
                                       "اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message("❌ خطأ في النظام",
                                   f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

    def show_success_message(self, title, message):
        """عرض رسالة نجاح متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة النجاح
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border-radius: 15px;
                border: 2px solid #34D399;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #10B981;
                border-radius: 8px;
                color: #047857;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #34D399;
            }
        """)

        msg_box.exec_()

    def show_error_message(self, title, message):
        """عرض رسالة خطأ متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة الخطأ
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.5 #B91C1C, stop:1 #991B1B);
                border-radius: 15px;
                border: 2px solid #F87171;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #DC2626;
                border-radius: 8px;
                color: #991B1B;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #F87171;
            }
        """)

        msg_box.exec_()

    def mousePressEvent(self, event):
        """بداية التحريك بالماوس"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """تحريك النافذة بالماوس"""
        if event.buttons() == Qt.LeftButton and self.dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """إنهاء التحريك بالماوس"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            event.accept()

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        elif event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        if self.session:
            self.session.close()
        event.accept()


# اختبار شاشة الدخول
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد قاعدة البيانات للاختبار
    from database import init_db
    init_db()

    login_screen = LoginScreen()
    login_screen.show()

    sys.exit(app.exec_())
